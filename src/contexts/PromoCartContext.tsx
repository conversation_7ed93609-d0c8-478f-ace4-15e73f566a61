import React, { createContext, useContext, useReducer, useCallback } from 'react';
import { OfferType } from '@/types';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';

// Types
interface PromoCartItem {
  offer: OfferType;
  quantity: number;
  price: string; // The effective price (clinicPrice or price)
}

interface PromoCartState {
  items: Record<string, PromoCartItem>; // keyed by offer.id
  isLoading: boolean;
}

type PromoCartAction =
  | { type: 'ADD_OFFER'; payload: { offer: OfferType; quantity: number } }
  | { type: 'UPDATE_QUANTITY'; payload: { offerId: string; quantity: number } }
  | { type: 'REMOVE_OFFER'; payload: { offerId: string } }
  | { type: 'CLEAR_ALL' }
  | { type: 'SET_LOADING'; payload: { isLoading: boolean } };

interface PromoCartContextValue {
  // State
  items: Record<string, PromoCartItem>;
  isLoading: boolean;
  
  // Computed values
  subtotal: number;
  totalItemCount: number;
  offerCount: number;
  
  // Actions
  addOffer: (offer: OfferType, quantity: number) => void;
  updateQuantity: (offerId: string, quantity: number) => void;
  removeOffer: (offerId: string) => void;
  clearAll: () => void;
  addAllToCart: () => Promise<void>;
  
  // Getters
  getOfferQuantity: (offerId: string) => number;
  hasOffers: boolean;
}

// Initial state
const initialState: PromoCartState = {
  items: {},
  isLoading: false,
};

// Reducer
function promoCartReducer(state: PromoCartState, action: PromoCartAction): PromoCartState {
  switch (action.type) {
    case 'ADD_OFFER': {
      const { offer, quantity } = action.payload;
      const price = offer.clinicPrice || offer.price;
      
      if (!price) {
        console.warn('Offer has no price, skipping:', offer.id);
        return state;
      }
      
      return {
        ...state,
        items: {
          ...state.items,
          [offer.id]: {
            offer,
            quantity,
            price,
          },
        },
      };
    }
    
    case 'UPDATE_QUANTITY': {
      const { offerId, quantity } = action.payload;
      
      if (quantity <= 0) {
        const { [offerId]: removed, ...remainingItems } = state.items;
        return {
          ...state,
          items: remainingItems,
        };
      }
      
      const existingItem = state.items[offerId];
      if (!existingItem) return state;
      
      return {
        ...state,
        items: {
          ...state.items,
          [offerId]: {
            ...existingItem,
            quantity,
          },
        },
      };
    }
    
    case 'REMOVE_OFFER': {
      const { offerId } = action.payload;
      const { [offerId]: removed, ...remainingItems } = state.items;
      
      return {
        ...state,
        items: remainingItems,
      };
    }
    
    case 'CLEAR_ALL': {
      return {
        ...state,
        items: {},
      };
    }
    
    case 'SET_LOADING': {
      return {
        ...state,
        isLoading: action.payload.isLoading,
      };
    }
    
    default:
      return state;
  }
}

// Context
const PromoCartContext = createContext<PromoCartContextValue | null>(null);

// Provider component
interface PromoCartProviderProps {
  children: React.ReactNode;
}

export const PromoCartProvider: React.FC<PromoCartProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(promoCartReducer, initialState);
  const { addToCart } = useCartStore();
  
  // Computed values
  const subtotal = Object.values(state.items).reduce((total, item) => {
    return total + (parseFloat(item.price) * item.quantity);
  }, 0);
  
  const totalItemCount = Object.values(state.items).reduce((total, item) => {
    return total + item.quantity;
  }, 0);
  
  const offerCount = Object.keys(state.items).length;
  const hasOffers = offerCount > 0;
  
  // Actions
  const addOffer = useCallback((offer: OfferType, quantity: number) => {
    dispatch({ type: 'ADD_OFFER', payload: { offer, quantity } });
  }, []);
  
  const updateQuantity = useCallback((offerId: string, quantity: number) => {
    dispatch({ type: 'UPDATE_QUANTITY', payload: { offerId, quantity } });
  }, []);
  
  const removeOffer = useCallback((offerId: string) => {
    dispatch({ type: 'REMOVE_OFFER', payload: { offerId } });
  }, []);
  
  const clearAll = useCallback(() => {
    dispatch({ type: 'CLEAR_ALL' });
  }, []);
  
  const getOfferQuantity = useCallback((offerId: string) => {
    return state.items[offerId]?.quantity || 0;
  }, [state.items]);
  
  const addAllToCart = useCallback(async () => {
    if (!hasOffers) return;
    
    dispatch({ type: 'SET_LOADING', payload: { isLoading: true } });
    
    try {
      // Add all offers to cart sequentially
      const promises = Object.values(state.items).map(({ offer, quantity }) => {
        return new Promise<void>((resolve, reject) => {
          addToCart({
            productOfferId: offer.id,
            quantity,
            onError: (message: string) => {
              console.error(`Failed to add offer ${offer.id} to cart:`, message);
              reject(new Error(message));
            },
          });
          // Since addToCart doesn't return a promise, we'll resolve immediately
          // In a real implementation, you might want to wait for the cart update
          resolve();
        });
      });
      
      await Promise.all(promises);
      
      // Clear the promotional cart after successful addition
      dispatch({ type: 'CLEAR_ALL' });
    } catch (error) {
      console.error('Failed to add some offers to cart:', error);
      // Don't clear the cart if there were errors
    } finally {
      dispatch({ type: 'SET_LOADING', payload: { isLoading: false } });
    }
  }, [state.items, hasOffers, addToCart]);
  
  const contextValue: PromoCartContextValue = {
    // State
    items: state.items,
    isLoading: state.isLoading,
    
    // Computed values
    subtotal,
    totalItemCount,
    offerCount,
    hasOffers,
    
    // Actions
    addOffer,
    updateQuantity,
    removeOffer,
    clearAll,
    addAllToCart,
    
    // Getters
    getOfferQuantity,
  };
  
  return (
    <PromoCartContext.Provider value={contextValue}>
      {children}
    </PromoCartContext.Provider>
  );
};

// Hook to use the context
export const usePromoCart = (): PromoCartContextValue => {
  const context = useContext(PromoCartContext);
  
  if (!context) {
    throw new Error('usePromoCart must be used within a PromoCartProvider');
  }
  
  return context;
};
