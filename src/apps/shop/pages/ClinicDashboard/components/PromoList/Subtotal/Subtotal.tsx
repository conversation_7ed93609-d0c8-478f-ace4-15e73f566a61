import { getPriceString } from '@/utils';
import { usePromoCart } from '@/contexts/PromoCartContext';

export const Subtotal = () => {
  const { subtotal, totalItemCount } = usePromoCart();

  // Calculate original price (assuming 20% discount for promotional pricing)
  const originalPrice = subtotal * 1.2;

  return (
    <div>
      <p className="mb-1 text-base">Subtotal ({totalItemCount} items)</p>
      <div className="flex items-center gap-2">
        <span className="mr-1 text-[32px] font-medium">
          {getPriceString(subtotal)}
        </span>
        <span className="relative mt-3 align-bottom text-base font-medium text-black/65 line-through">
          {getPriceString(originalPrice)}
        </span>
      </div>
    </div>
  );
};
