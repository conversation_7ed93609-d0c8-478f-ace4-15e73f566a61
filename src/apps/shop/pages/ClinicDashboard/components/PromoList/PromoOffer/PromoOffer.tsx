import {
  AddToCartInput,
  AddToCartInputProps,
} from '@/libs/products/components/AddToCartInput/AddToCartInput';
import { OfferType } from '@/types';
import { getPriceString } from '@/utils';
import { useCallback, useState } from 'react';
import { usePromoCart } from '@/contexts/PromoCartContext';

type PromoOfferProps = {
  offer: OfferType;
};

export const PromoOffer = ({ offer }: PromoOfferProps) => {
  const { addOffer, updateQuantity, getOfferQuantity } = usePromoCart();
  const currentQuantity = getOfferQuantity(offer.id);
  const [amount, setAmount] = useState(
    currentQuantity || offer.increments || 1,
  );

  const handleQuantityUpdate: AddToCartInputProps['onUpdate'] = useCallback(
    ({ amount: newAmount }) => {
      setAmount(newAmount);

      // Update the promo cart context
      if (newAmount > 0) {
        if (currentQuantity === 0) {
          // Add new offer to promo cart
          addOffer(offer, newAmount);
        } else {
          // Update existing offer quantity
          updateQuantity(offer.id, newAmount);
        }
      } else {
        // Remove offer if quantity is 0
        updateQuantity(offer.id, 0);
      }
    },
    [addOffer, updateQuantity, offer, currentQuantity],
  );

  const itemPrice = offer.price || offer.clinicPrice;
  if (!itemPrice) return null;

  return (
    <div className="grid h-[72px] w-full grid-cols-[auto_1fr_auto] items-center gap-6 bg-white p-4">
      <div className="h-12">
        <img
          src={
            offer.vendor.imageUrl ||
            'https://staging.services.highfive.vet/storage/vendor-images/mwi.png'
          }
          className="h-full"
          alt={`${offer.vendor.name} logo`}
        />
      </div>
      <div>
        <p className="max-w-96 text-sm font-medium text-black">{offer.name}</p>
        <div className="flex items-center gap-4 divide-x-1 divide-solid divide-black/10">
          <span>
            <span className="text-xs text-black/65">SKU: </span>
            <span className="mr-3 text-xs font-medium text-black">
              {offer.vendorSku}
            </span>
          </span>
          <span className="text-xs text-black">{offer.vendor.name}</span>
        </div>
      </div>
      <div className="flex max-w-40 items-center gap-3">
        <span className="text-sm font-medium">{getPriceString(itemPrice)}</span>
        <AddToCartInput
          originalAmount={amount}
          minIncrement={amount}
          onUpdate={handleQuantityUpdate}
        />
      </div>
    </div>
  );
};
