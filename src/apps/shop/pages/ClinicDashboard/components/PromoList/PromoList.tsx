import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { PoweredBy } from '@/libs/ui/PoweredBy/PoweredBy';
import { PromoItem } from './PromoItem/PromoItem';
import { PromoType } from '@/types/common';

const promoData: PromoType[] = [
  {
    id: '01968b1d-612c-7278-91f3-8cb0b116fcc1',
    name: "Save 15% on Hill's Science Diet when you buy 20+ units",
    type: 'BOGO',
    description:
      "Based on your purchase history, you frequently order Hill's Science Diet products. This bulk discount can save you significant money on your regular orders. Valid for all Hill's Science Diet dry and wet food products.",
    startedAt: '2025-01-01',
    endedAt: '2025-04-15',
    vendor: {
      id: '9d7559b3-2608-4f66-a75a-1f1b77cd472e',
      name: "<PERSON>'s Pet Nutrition",
      imageUrl:
        'https://staging.services.highfive.vet/storage/vendor-images/hills.png',
    },
    offers: [
      {
        id: '9d792653-7c97-441a-86ec-0b3a934a006e',
        vendor: {
          id: '9d7559b3-0737-464e-91ba-739b63ec41cd',
          name: 'MWI',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/mwi.png',
          type: 'distributor',
        },
        vendorSku: '099865',
        price: '608.75',
        clinicPrice: '608.75',
        stockStatus: 'IN_STOCK',
        lastOrderedAt: '2024-06-24T00:00:00.000000Z',
        lastOrderedQuantity: 2,
        increments: 2,
        isRecommended: false,
        rebatePercent: ' 8.0',
      },
      {
        id: '9d757227-01c8-40b1-a193-def0cff756fa',
        vendor: {
          id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
          name: 'Zoetis',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
          type: 'manufacturer',
        },
        vendorSku: '10022680',
        price: '121.75',
        clinicPrice: '0.00',
        stockStatus: 'IN_STOCK',
        lastOrderedAt: '2024-02-29T00:00:00.000000Z',
        lastOrderedQuantity: 10,
        increments: 5,
        isRecommended: false,
        rebatePercent: '8.0',
      },
      {
        id: '9d7ed145-7d79-404e-bc3c-a054c37bad61',
        vendor: {
          id: '9d7559b3-0b71-4606-88b0-e903fc518846',
          name: 'Covetrus',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/covetrus.png',
          type: 'distributor',
        },
        vendorSku: '071605',
        price: '608.70',
        clinicPrice: '608.75',
        stockStatus: 'IN_STOCK',
        lastOrderedAt: '2022-08-01T00:00:00.000000Z',
        lastOrderedQuantity: 1,
        increments: 1,
        isRecommended: false,
        rebatePercent: null,
      },
    ],
  },
  {
    id: '01968b1d-612c-7278-91f3-8cb0b116fcc2',
    name: 'Spring Flea & Tick Prevention Bundle - 25% off',
    type: 'BOGO',
    description:
      'Get ready for flea and tick season! Bundle any 3 flea and tick prevention products and save 25%. Perfect timing for your spring inventory restocking. Includes popular brands like Bravecto, NexGard, and Seresto.',
    startedAt: '2025-03-01',
    endedAt: '2025-05-30',
    vendor: {
      id: '9d7559b3-2608-4f66-a75a-1f1b77cd472f',
      name: 'Multiple Vendors',
      imageUrl:
        'https://staging.services.highfive.vet/storage/vendor-images/multiple.png',
    },
    offers: [
      {
        id: '9d792653-7c97-441a-86ec-0b3a934a006e',
        vendor: {
          id: '9d7559b3-0737-464e-91ba-739b63ec41cd',
          name: 'MWI',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/mwi.png',
          type: 'distributor',
        },
        vendorSku: '099865',
        price: '608.75',
        clinicPrice: '608.75',
        stockStatus: 'IN_STOCK',
        lastOrderedAt: '2024-06-24T00:00:00.000000Z',
        lastOrderedQuantity: 2,
        increments: 2,
        isRecommended: false,
        rebatePercent: '8.0',
      },
      {
        id: '9d757227-01c8-40b1-a193-def0cff756fa',
        vendor: {
          id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
          name: 'Zoetis',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
          type: 'manufacturer',
        },
        vendorSku: '10022680',
        price: '121.75',
        clinicPrice: '0.00',
        stockStatus: 'IN_STOCK',
        lastOrderedAt: '2024-02-29T00:00:00.000000Z',
        lastOrderedQuantity: 10,
        increments: 5,
        isRecommended: false,
        rebatePercent: '8.0',
      },
      {
        id: '9d7ed145-7d79-404e-bc3c-a054c37bad61',
        vendor: {
          id: '9d7559b3-0b71-4606-88b0-e903fc518846',
          name: 'Covetrus',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/covetrus.png',
          type: 'distributor',
        },
        vendorSku: '071605',
        price: '608.70',
        clinicPrice: '608.75',
        stockStatus: 'IN_STOCK',
        lastOrderedAt: '2022-08-01T00:00:00.000000Z',
        lastOrderedQuantity: 1,
        increments: 1,
        isRecommended: false,
        rebatePercent: null,
      },
    ],
  },
  {
    id: '01968b1d-612c-7278-91f3-8cb0b116fcc3',
    name: 'Try the new Royal Canin Digestive Care - 20% off first order',
    type: 'BOGO',
    description:
      "Introduce your clients to Royal Canin's latest digestive health formula. This new product has shown excellent results in clinical trials for pets with sensitive stomachs. Limited time introductory pricing.",
    startedAt: '2025-04-01',
    endedAt: '2025-06-10',
    vendor: {
      id: '9d7559b3-2608-4f66-a75a-1f1b77cd4730',
      name: 'Royal Canin',
      imageUrl:
        'https://staging.services.highfive.vet/storage/vendor-images/royal-canin.png',
    },
    offers: [
      {
        id: '9d792653-7c97-441a-86ec-0b3a934a006e',
        vendor: {
          id: '9d7559b3-0737-464e-91ba-739b63ec41cd',
          name: 'MWI',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/mwi.png',
          type: 'distributor',
        },
        vendorSku: '099865',
        price: '608.75',
        clinicPrice: '608.75',
        stockStatus: 'IN_STOCK',
        lastOrderedAt: '2024-06-24T00:00:00.000000Z',
        lastOrderedQuantity: 2,
        increments: 2,
        isRecommended: false,
        rebatePercent: '8.0',
      },
      {
        id: '9d757227-01c8-40b1-a193-def0cff756fa',
        vendor: {
          id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
          name: 'Zoetis',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
          type: 'manufacturer',
        },
        vendorSku: '10022680',
        price: '121.75',
        clinicPrice: '0.00',
        stockStatus: 'IN_STOCK',
        lastOrderedAt: '2024-02-29T00:00:00.000000Z',
        lastOrderedQuantity: 10,
        increments: 5,
        isRecommended: false,
        rebatePercent: '8.0',
      },
      {
        id: '9d7ed145-7d79-404e-bc3c-a054c37bad61',
        vendor: {
          id: '9d7559b3-0b71-4606-88b0-e903fc518846',
          name: 'Covetrus',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/covetrus.png',
          type: 'distributor',
        },
        vendorSku: '071605',
        price: '608.70',
        clinicPrice: '608.75',
        stockStatus: 'IN_STOCK',
        lastOrderedAt: '2022-08-01T00:00:00.000000Z',
        lastOrderedQuantity: 1,
        increments: 1,
        isRecommended: false,
        rebatePercent: null,
      },
    ],
  },
  {
    id: '01968b1d-612c-7278-91f3-8cb0b116fcc4',
    name: 'VIP Customer Exclusive: 30% off premium surgical supplies',
    type: 'BOGO',
    description:
      'As one of our valued VIP customers, enjoy exclusive access to premium surgical supplies at a significant discount. This offer includes sutures, surgical instruments, and sterile drapes from top manufacturers.',
    startedAt: '2025-06-01',
    endedAt: '2025-07-20',
    vendor: {
      id: '9d7559b3-2608-4f66-a75a-1f1b77cd4731',
      name: 'MedVet Supplies',
      imageUrl:
        'https://staging.services.highfive.vet/storage/vendor-images/medvet.png',
    },
    offers: [
      {
        id: '9d792653-7c97-441a-86ec-0b3a934a006e',
        vendor: {
          id: '9d7559b3-0737-464e-91ba-739b63ec41cd',
          name: 'MWI',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/mwi.png',
          type: 'distributor',
        },
        vendorSku: '099865',
        price: '608.75',
        clinicPrice: '608.75',
        stockStatus: 'IN_STOCK',
        lastOrderedAt: '2024-06-24T00:00:00.000000Z',
        lastOrderedQuantity: 2,
        increments: 2,
        isRecommended: false,
        rebatePercent: '8.0',
      },
      {
        id: '9d757227-01c8-40b1-a193-def0cff756fa',
        vendor: {
          id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
          name: 'Zoetis',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
          type: 'manufacturer',
        },
        vendorSku: '10022680',
        price: '121.75',
        clinicPrice: '0.00',
        stockStatus: 'IN_STOCK',
        lastOrderedAt: '2024-02-29T00:00:00.000000Z',
        lastOrderedQuantity: 10,
        increments: 5,
        isRecommended: false,
        rebatePercent: '8.0',
      },
      {
        id: '9d7ed145-7d79-404e-bc3c-a054c37bad61',
        vendor: {
          id: '9d7559b3-0b71-4606-88b0-e903fc518846',
          name: 'Covetrus',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/covetrus.png',
          type: 'distributor',
        },
        vendorSku: '071605',
        price: '608.70',
        clinicPrice: '608.75',
        stockStatus: 'IN_STOCK',
        lastOrderedAt: '2022-08-01T00:00:00.000000Z',
        lastOrderedQuantity: 1,
        increments: 1,
        isRecommended: false,
        rebatePercent: null,
      },
    ],
  },
];

export const PromoList = () => {
  return (
    <CollapsiblePanel
      header={
        <>
          <h3 className="py-[1.3rem] pr-4 pl-6 text-xl font-medium">
            Promo Matcher
          </h3>
          <PoweredBy className="relative mb-2 h-4 text-sm" />
        </>
      }
      content={
        <div className="flex flex-col">
          <div className="flex-col bg-white p-6">
            <h3 className="text-lg font-medium">Handpicked Deals for You!</h3>
            <p className="mb-8 text-sm text-black/80">
              We found the best deals for you - don&apos;t wait!
            </p>
            <div className="space-y-4 rounded-sm bg-black/2 p-4">
              {promoData.map((promo) => (
                <PromoItem key={promo.id} {...promo} />
              ))}
            </div>
          </div>
        </div>
      }
      startOpen
    />
  );
};
