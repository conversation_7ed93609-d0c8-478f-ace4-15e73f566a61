import { AddToCartButton } from '@/libs/products/components/AddToCartForm/components/AddToCartButton/AddToCartButton';
import { Button } from '@/libs/ui/Button/Button';
import { Modal } from '@/components';
import { MODAL_NAME } from '@/constants';
import {
  useModalStore,
  type ModalOptionProps,
} from '@/apps/shop/stores/useModalStore';
import styles from './PromoDetailsModal.module.css';
import { PromoOffer } from '../PromoOffer/PromoOffer';
import { PromoTitle } from '../PromoTitle/PromoTitle';
import { Subtotal } from '../Subtotal/Subtotal';
import { PromoType } from '@/types/common';
import { PromoCartProvider, usePromoCart } from '@/contexts/PromoCartContext';

type PromoOfferModalOptions = ModalOptionProps & {
  promoType: PromoType['type'];
  title: string;
  offers: PromoType['offers'];
};

// Inner component that uses the PromoCart context
const PromoDetailsModalContent = () => {
  const { modalOption } = useModalStore();
  const { promoType, title, offers } = modalOption as PromoOfferModalOptions;
  const { subtotal, totalItemCount, addAllToCart, isLoading, hasOffers } =
    usePromoCart();

  if (!offers) return null;

  // Calculate original price (assuming no discount for now - this could be enhanced)
  const originalPrice = subtotal * 1.2; // Example: 20% discount

  const handleAddAllToCart = async () => {
    await addAllToCart();
  };

  return (
    <Modal
      name={MODAL_NAME.PROMO_MATCHER_PRODUCTS}
      size="auto"
      withCloseButton
      customClasses={{ header: styles.modalHeader, body: styles.modalBody }}
    >
      <div className="flex flex-col">
        <div className="flex-col bg-white p-6 pt-0">
          <h3 className="mb-2 text-2xl font-medium">
            You&apos;re Almost There!
          </h3>
          <p className="mb-6 text-sm text-black/65">
            Follow the steps below to claim your savings before this offer
            expires.
          </p>
          <div className="space-y-4 rounded-lg border-2 border-black/10 bg-black/2.5 p-6">
            <PromoTitle promoType={promoType} title={title} />
            <div className="divider-h"></div>
            <div className="grid gap-2">
              {offers.map((offer) => (
                <PromoOffer key={offer.id} offer={offer} />
              ))}
            </div>
            <p className="mr-2 mb-4 inline-block text-sm">
              Buy now and get <strong>9 for free!</strong>
            </p>
            <Button className={styles.seeList} variant="unstyled">
              See List
            </Button>
            <div className="divider-h"></div>
            <div className="flex items-center justify-between">
              <Subtotal
                subtotal={subtotal}
                originalPrice={originalPrice}
                itemsCount={totalItemCount}
              />
              <div className="w-52">
                <AddToCartButton
                  quantityInCart={totalItemCount}
                  isLoading={isLoading}
                  fullWidth
                  onClick={handleAddAllToCart}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

// Main component that provides the context
export const PromoDetailsModal = () => {
  return (
    <PromoCartProvider>
      <PromoDetailsModalContent />
    </PromoCartProvider>
  );
};
