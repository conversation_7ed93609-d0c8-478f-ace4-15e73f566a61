import { AddToCartButton } from '@/libs/products/components/AddToCartForm/components/AddToCartButton/AddToCartButton';
import { Button } from '@/libs/ui/Button/Button';
import { Modal } from '@/components';
import { MODAL_NAME } from '@/constants';
import {
  useModalStore,
  type ModalOptionProps,
} from '@/apps/shop/stores/useModalStore';
import styles from './PromoDetailsModal.module.css';
import { PromoOffer } from '../PromoOffer/PromoOffer';
import { PromoTitle } from '../PromoTitle/PromoTitle';
import { Subtotal } from '../Subtotal/Subtotal';
import { PromoType } from '@/types/common';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { FormEventHandler, useCallback, useState } from 'react';
import { useCartProductMapState } from '@/libs/cart/hooks/useCartProductMapState';
import { AddToCartInputProps } from '@/libs/products/components/AddToCartInput/AddToCartInput';

type PromoOfferModalOptions = ModalOptionProps & {
  promoType: PromoType['type'];
  title: string;
  offers: PromoType['offers'];
};

export const PromoDetailsModal = () => {
  const { modalOption } = useModalStore();
  const { promoType, title, offers } = modalOption as PromoOfferModalOptions;
  const { addToCart, updatingProductIds } = useCartStore();
  const [offerId, setOfferId] = useState();
  const cartProductMapState = useCartProductMapState();
  const isUpdating = offerId ? updatingProductIds.has(offerId) : false;

  const offerQuantityOnCart = offerId
    ? (cartProductMapState[offerId]?.quantity ?? 0)
    : 0;

  if (!offers) return null;

  return (
    <Modal
      name={MODAL_NAME.PROMO_MATCHER_PRODUCTS}
      size="auto"
      withCloseButton
      customClasses={{ header: styles.modalHeader, body: styles.modalBody }}
    >
      <div className="flex flex-col">
        <div className="flex-col bg-white p-6 pt-0">
          <h3 className="mb-2 text-2xl font-medium">
            You&apos;re Almost There!
          </h3>
          <p className="mb-6 text-sm text-black/65">
            Follow the steps below to claim your savings before this offer
            expires.
          </p>
          <div className="space-y-4 rounded-lg border-2 border-black/10 bg-black/2.5 p-6">
            <PromoTitle promoType={promoType} title={title} />
            <div className="divider-h"></div>
            <div className="grid gap-2">
              {offers.map((offer) => (
                <PromoOffer key={offer.id} offer={offer} />
              ))}
            </div>
            <p className="mr-2 mb-4 inline-block text-sm">
              Buy now and get <strong>9 for free!</strong>
            </p>
            <Button className={styles.seeList} variant="unstyled">
              See List
            </Button>
            <div className="divider-h"></div>
            <div className="flex items-center justify-between">
              <Subtotal
                subtotal={129.34}
                originalPrice={240.12}
                itemsCount={offers.length}
              />
              <div className="w-52">
                <AddToCartButton
                  quantityInCart={offerQuantityOnCart}
                  isLoading={isUpdating}
                  fullWidth
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};
